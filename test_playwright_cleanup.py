#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 Playwright 资源清理
"""

import asyncio
import psutil
import os
import tempfile
import time
from pathlib import Path

try:
    from playwright.async_api import async_playwright
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    print("❌ Playwright 未安装，无法测试")

async def test_playwright_cleanup():
    """测试 Playwright 资源清理"""
    if not PLAYWRIGHT_AVAILABLE:
        return
    
    print("🧪 开始测试 Playwright 资源清理...")
    
    # 获取测试前的进程信息
    initial_processes = get_browser_processes()
    initial_temp_files = count_temp_files()
    
    print(f"📊 测试前状态:")
    print(f"  浏览器相关进程: {len(initial_processes)}")
    print(f"  临时文件数量: {initial_temp_files}")
    
    playwright = None
    browser = None
    context = None
    page = None
    
    try:
        # 启动 Playwright
        print("\n🚀 启动 Playwright...")
        playwright = await async_playwright().start()
        
        # 启动浏览器
        print("🌐 启动浏览器...")
        browser = await playwright.chromium.launch(headless=True)
        
        # 创建上下文
        print("📄 创建浏览器上下文...")
        context = await browser.new_context()
        
        # 创建页面
        print("📃 创建页面...")
        page = await context.new_page()
        
        # 访问一个简单页面
        print("🔗 访问测试页面...")
        await page.goto("data:text/html,<h1>Test Page</h1>")
        
        # 检查资源使用情况
        current_processes = get_browser_processes()
        current_temp_files = count_temp_files()
        
        print(f"\n📊 运行时状态:")
        print(f"  浏览器相关进程: {len(current_processes)}")
        print(f"  临时文件数量: {current_temp_files}")
        print(f"  新增进程: {len(current_processes) - len(initial_processes)}")
        print(f"  新增临时文件: {current_temp_files - initial_temp_files}")
        
        # 等待一下
        await asyncio.sleep(1)
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
    
    finally:
        # 清理资源（正确的顺序）
        print("\n🧹 开始清理资源...")
        
        try:
            if page:
                print("  关闭页面...")
                await page.close()
                page = None
        except Exception as e:
            print(f"  ⚠️ 关闭页面失败: {e}")
        
        try:
            if context:
                print("  关闭上下文...")
                await context.close()
                context = None
        except Exception as e:
            print(f"  ⚠️ 关闭上下文失败: {e}")
        
        try:
            if browser:
                print("  关闭浏览器...")
                await browser.close()
                browser = None
        except Exception as e:
            print(f"  ⚠️ 关闭浏览器失败: {e}")
        
        try:
            if playwright:
                print("  🔧 停止 Playwright 实例...")
                await playwright.stop()
                playwright = None
                print("  ✅ Playwright 实例已停止")
        except Exception as e:
            print(f"  ❌ 停止 Playwright 实例失败: {e}")
    
    # 等待进程完全退出
    print("\n⏳ 等待进程完全退出...")
    await asyncio.sleep(3)
    
    # 检查清理后的状态
    final_processes = get_browser_processes()
    final_temp_files = count_temp_files()
    
    print(f"\n📊 清理后状态:")
    print(f"  浏览器相关进程: {len(final_processes)}")
    print(f"  临时文件数量: {final_temp_files}")
    print(f"  剩余进程: {len(final_processes) - len(initial_processes)}")
    print(f"  剩余临时文件: {final_temp_files - initial_temp_files}")
    
    # 分析结果
    if len(final_processes) <= len(initial_processes):
        print("✅ 进程清理成功")
    else:
        print("❌ 存在进程泄漏")
        print("剩余进程:")
        for proc in final_processes:
            if proc not in initial_processes:
                try:
                    p = psutil.Process(proc)
                    print(f"  PID {proc}: {p.name()} - {' '.join(p.cmdline()[:3])}")
                except:
                    print(f"  PID {proc}: (进程已退出)")
    
    temp_diff = final_temp_files - initial_temp_files
    if temp_diff <= 5:  # 允许少量临时文件残留
        print("✅ 临时文件清理基本成功")
    else:
        print(f"⚠️ 可能存在临时文件泄漏 (+{temp_diff} 个文件)")

def get_browser_processes():
    """获取浏览器相关进程"""
    browser_names = ['chrome', 'chromium', 'firefox', 'msedge', 'playwright']
    processes = []
    
    try:
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                name = proc.info['name'].lower()
                cmdline = ' '.join(proc.info['cmdline'] or []).lower()
                
                if any(browser in name or browser in cmdline for browser in browser_names):
                    processes.append(proc.info['pid'])
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
    except Exception as e:
        print(f"获取进程信息失败: {e}")
    
    return processes

def count_temp_files():
    """统计临时文件数量"""
    try:
        temp_dir = Path(tempfile.gettempdir())
        count = 0
        
        # 统计 playwright 相关的临时文件
        for pattern in ['playwright*', 'chrome*', 'chromium*']:
            count += len(list(temp_dir.glob(pattern)))
        
        return count
    except Exception as e:
        print(f"统计临时文件失败: {e}")
        return 0

async def main():
    print("🧪 Playwright 资源清理测试工具")
    print("=" * 50)
    
    await test_playwright_cleanup()
    
    print("\n🎉 测试完成！")
    print("\n💡 如果看到进程或文件泄漏，说明需要进一步优化清理逻辑")

if __name__ == "__main__":
    if PLAYWRIGHT_AVAILABLE:
        asyncio.run(main())
    else:
        print("请先安装 Playwright: pip install playwright")
        print("然后安装浏览器: playwright install")
