#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试导出格式选择功能的脚本
"""

import sys
from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PySide6.QtWidgets import Q<PERSON>ialog, QVBoxLayout, QHBoxLayout, QRadioButton, QPushButton, QLabel, QGroupBox

class FormatSelectionDialog(QDialog):
    """格式选择对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("选择导出格式")
        self.setModal(True)
        self.resize(400, 300)
        self.selected_format = 'xlsx'  # 默认选择
        
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("请选择导出文件格式：")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px; margin-bottom: 10px;")
        layout.addWidget(title_label)
        
        # 格式选择组
        format_group = QGroupBox("文件格式")
        format_layout = QVBoxLayout(format_group)
        
        # XLSX选项
        self.xlsx_radio = QRadioButton("📊 Excel格式 (.xlsx)")
        self.xlsx_radio.setChecked(True)  # 默认选中
        self.xlsx_radio.setStyleSheet("font-size: 12px; padding: 8px;")
        
        xlsx_desc = QLabel("• 原生Excel格式，支持样式和格式化\n• 表头美化，自动列宽调整\n• 推荐用于数据分析和展示")
        xlsx_desc.setStyleSheet("color: #666; font-size: 10px; margin-left: 20px; margin-bottom: 10px;")
        xlsx_desc.setWordWrap(True)
        
        format_layout.addWidget(self.xlsx_radio)
        format_layout.addWidget(xlsx_desc)
        
        # CSV选项
        self.csv_radio = QRadioButton("📄 CSV格式 (.csv)")
        self.csv_radio.setStyleSheet("font-size: 12px; padding: 8px;")
        
        csv_desc = QLabel("• 通用文本格式，兼容性最好\n• 可在任何表格软件中打开\n• 文件体积更小，处理速度快")
        csv_desc.setStyleSheet("color: #666; font-size: 10px; margin-left: 20px; margin-bottom: 10px;")
        csv_desc.setWordWrap(True)
        
        format_layout.addWidget(self.csv_radio)
        format_layout.addWidget(csv_desc)
        
        layout.addWidget(format_group)
        
        # 依赖检查提示
        try:
            import openpyxl
            xlsx_available = True
            print(f"✅ openpyxl可用，版本: {openpyxl.__version__}")
        except ImportError:
            xlsx_available = False
            print("❌ openpyxl不可用")
        
        if not xlsx_available:
            warning_label = QLabel("⚠️ 注意：Excel格式需要安装openpyxl库\n安装命令：pip install openpyxl")
            warning_label.setStyleSheet("color: #ff6b35; font-size: 10px; background-color: #fff3e0; padding: 8px; border-radius: 4px;")
            warning_label.setWordWrap(True)
            layout.addWidget(warning_label)
            
            # 如果没有openpyxl，默认选择CSV
            self.xlsx_radio.setChecked(False)
            self.csv_radio.setChecked(True)
            self.selected_format = 'csv'
        
        # 按钮
        button_layout = QHBoxLayout()
        
        ok_button = QPushButton("确定")
        ok_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; padding: 8px 16px; border: none; border-radius: 4px; font-weight: bold; } QPushButton:hover { background-color: #45a049; }")
        cancel_button = QPushButton("取消")
        cancel_button.setStyleSheet("QPushButton { background-color: #f44336; color: white; padding: 8px 16px; border: none; border-radius: 4px; font-weight: bold; } QPushButton:hover { background-color: #da190b; }")
        
        button_layout.addStretch()
        button_layout.addWidget(ok_button)
        button_layout.addWidget(cancel_button)
        
        layout.addLayout(button_layout)
        
        # 事件处理
        self.xlsx_radio.toggled.connect(self.on_format_changed)
        self.csv_radio.toggled.connect(self.on_format_changed)
        
        ok_button.clicked.connect(self.accept)
        cancel_button.clicked.connect(self.reject)
    
    def on_format_changed(self):
        if self.xlsx_radio.isChecked():
            self.selected_format = 'xlsx'
        else:
            self.selected_format = 'csv'
        print(f"选择格式: {self.selected_format}")

class TestWindow(QMainWindow):
    """测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("导出格式选择测试")
        self.setGeometry(100, 100, 300, 200)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        test_button = QPushButton("测试格式选择对话框")
        test_button.clicked.connect(self.test_format_dialog)
        layout.addWidget(test_button)
        
        self.result_label = QLabel("点击按钮测试格式选择")
        self.result_label.setStyleSheet("padding: 10px; background-color: #f0f0f0; border-radius: 4px;")
        layout.addWidget(self.result_label)
    
    def test_format_dialog(self):
        """测试格式选择对话框"""
        dialog = FormatSelectionDialog(self)
        
        if dialog.exec() == QDialog.Accepted:
            selected_format = dialog.selected_format
            self.result_label.setText(f"✅ 用户选择了: {selected_format.upper()} 格式")
            print(f"用户确认选择: {selected_format}")
        else:
            self.result_label.setText("❌ 用户取消了导出")
            print("用户取消了选择")

def main():
    app = QApplication(sys.argv)
    
    print("=== 导出格式选择功能测试 ===")
    
    # 检查依赖
    try:
        import openpyxl
        print(f"✅ openpyxl已安装，版本: {openpyxl.__version__}")
    except ImportError:
        print("❌ openpyxl未安装")
        print("安装命令: pip install openpyxl")
    
    window = TestWindow()
    window.show()
    
    print("\n使用说明:")
    print("1. 点击按钮打开格式选择对话框")
    print("2. 选择XLSX或CSV格式")
    print("3. 点击确定或取消")
    print("4. 查看选择结果")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
