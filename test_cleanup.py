#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试程序退出清理功能
"""

import sys
import time
from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QTextEdit
from PySide6.QtCore import QTimer

class TestCleanupWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("测试程序退出清理")
        self.setGeometry(100, 100, 400, 300)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 创建日志显示
        self.log_text = QTextEdit()
        layout.addWidget(self.log_text)
        
        # 创建测试按钮
        test_btn = QPushButton("测试强制退出")
        test_btn.clicked.connect(self.test_force_exit)
        layout.addWidget(test_btn)
        
        # 创建定时器模拟后台任务
        self.timer = QTimer()
        self.timer.timeout.connect(self.background_task)
        self.timer.start(1000)  # 每秒执行一次
        
        self.task_count = 0
        self.add_log("程序启动，后台任务开始运行...")
    
    def add_log(self, message):
        """添加日志"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
    
    def background_task(self):
        """模拟后台任务"""
        self.task_count += 1
        self.add_log(f"后台任务执行中... 第{self.task_count}次")
    
    def test_force_exit(self):
        """测试强制退出"""
        self.add_log("准备强制退出程序...")
        QTimer.singleShot(1000, self.close)  # 1秒后关闭
    
    def closeEvent(self, event):
        """关闭事件 - 测试清理逻辑"""
        self.add_log("🔄 正在关闭程序...")
        
        # 停止定时器
        if hasattr(self, 'timer'):
            self.timer.stop()
            self.add_log("⏹️ 后台定时器已停止")
        
        # 模拟清理其他资源
        self.add_log("🧹 清理其他资源...")
        
        self.add_log("✅ 程序已安全关闭，所有资源已清理")
        event.accept()

def main():
    app = QApplication(sys.argv)
    window = TestCleanupWindow()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
