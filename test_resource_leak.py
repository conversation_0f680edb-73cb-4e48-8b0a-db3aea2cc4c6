#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的资源泄漏测试
"""

import asyncio
import psutil
import time

try:
    from playwright.async_api import async_playwright
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False

class TestAlarmFetcher:
    """模拟 AlarmFetcher 的资源管理"""
    
    def __init__(self):
        self.playwright = None
        self.browser = None
        self.context = None
        self.page = None
    
    async def start_browser(self):
        """启动浏览器（修复前的方式）"""
        print("🚀 启动浏览器（旧方式）...")
        playwright = await async_playwright().start()  # 没有保存引用！
        self.browser = await playwright.chromium.launch(headless=True)
        self.context = await self.browser.new_context()
        self.page = await self.context.new_page()
    
    async def start_browser_fixed(self):
        """启动浏览器（修复后的方式）"""
        print("🚀 启动浏览器（新方式）...")
        self.playwright = await async_playwright().start()  # 保存引用
        self.browser = await self.playwright.chromium.launch(headless=True)
        self.context = await self.browser.new_context()
        self.page = await self.context.new_page()
    
    async def cleanup_old(self):
        """旧的清理方式（有泄漏）"""
        print("🧹 旧清理方式...")
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            # 注意：没有清理 playwright 实例！
        except Exception as e:
            print(f"清理出错: {e}")
    
    async def cleanup_fixed(self):
        """新的清理方式（修复泄漏）"""
        print("🧹 新清理方式...")
        try:
            if self.page:
                await self.page.close()
                self.page = None
            if self.context:
                await self.context.close()
                self.context = None
            if self.browser:
                await self.browser.close()
                self.browser = None
            # 🔧 修复：清理 playwright 实例
            if self.playwright:
                await self.playwright.stop()
                self.playwright = None
                print("✅ Playwright 实例已清理")
        except Exception as e:
            print(f"清理出错: {e}")

def count_chrome_processes():
    """统计 Chrome 相关进程数量"""
    count = 0
    try:
        for proc in psutil.process_iter(['name']):
            name = proc.info['name'].lower()
            if 'chrome' in name or 'chromium' in name:
                count += 1
    except:
        pass
    return count

async def test_resource_leak():
    """测试资源泄漏"""
    if not PLAYWRIGHT_AVAILABLE:
        print("❌ Playwright 未安装")
        return
    
    print("🧪 测试资源泄漏修复效果")
    print("=" * 40)
    
    initial_processes = count_chrome_processes()
    print(f"初始 Chrome 进程数: {initial_processes}")
    
    # 测试旧方式（有泄漏）
    print("\n1️⃣ 测试旧方式（有泄漏）:")
    fetcher1 = TestAlarmFetcher()
    await fetcher1.start_browser()
    await fetcher1.page.goto("data:text/html,<h1>Test 1</h1>")
    
    after_start1 = count_chrome_processes()
    print(f"启动后进程数: {after_start1} (+{after_start1 - initial_processes})")
    
    await fetcher1.cleanup_old()
    await asyncio.sleep(2)  # 等待进程退出
    
    after_cleanup1 = count_chrome_processes()
    print(f"清理后进程数: {after_cleanup1} (剩余: {after_cleanup1 - initial_processes})")
    
    if after_cleanup1 > initial_processes:
        print("❌ 检测到进程泄漏！")
    else:
        print("✅ 无进程泄漏")
    
    # 测试新方式（已修复）
    print("\n2️⃣ 测试新方式（已修复）:")
    fetcher2 = TestAlarmFetcher()
    await fetcher2.start_browser_fixed()
    await fetcher2.page.goto("data:text/html,<h1>Test 2</h1>")
    
    after_start2 = count_chrome_processes()
    print(f"启动后进程数: {after_start2} (+{after_start2 - after_cleanup1})")
    
    await fetcher2.cleanup_fixed()
    await asyncio.sleep(2)  # 等待进程退出
    
    after_cleanup2 = count_chrome_processes()
    print(f"清理后进程数: {after_cleanup2} (剩余: {after_cleanup2 - after_cleanup1})")
    
    if after_cleanup2 > after_cleanup1:
        print("❌ 仍有进程泄漏")
    else:
        print("✅ 进程清理成功")
    
    print(f"\n📊 总结:")
    print(f"  初始进程: {initial_processes}")
    print(f"  旧方式后: {after_cleanup1} (泄漏: {after_cleanup1 - initial_processes})")
    print(f"  新方式后: {after_cleanup2} (泄漏: {after_cleanup2 - after_cleanup1})")
    
    total_leak = after_cleanup2 - initial_processes
    if total_leak <= 0:
        print("🎉 资源泄漏修复成功！")
    else:
        print(f"⚠️ 仍有 {total_leak} 个进程泄漏")

if __name__ == "__main__":
    if PLAYWRIGHT_AVAILABLE:
        asyncio.run(test_resource_leak())
    else:
        print("请先安装 Playwright:")
        print("pip install playwright")
        print("playwright install")
