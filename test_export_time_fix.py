#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试导出时间格式和数字格式修复的脚本
"""

import sqlite3
from datetime import datetime

def test_time_format():
    """测试时间格式化函数"""
    print("=== 测试时间格式化 ===")
    
    # 模拟format_alarm_time函数
    def format_alarm_time(alarm_time):
        try:
            if not alarm_time:
                return ""
            
            if isinstance(alarm_time, (int, float)) and alarm_time > 0:
                # 处理时间戳
                if alarm_time > 1000000000000:  # 毫秒时间戳
                    timestamp = alarm_time / 1000
                else:  # 秒时间戳
                    timestamp = alarm_time
                
                dt = datetime.fromtimestamp(timestamp)
                return dt.strftime("%Y-%m-%d %H:%M:%S")
            elif isinstance(alarm_time, str):
                # 如果已经是字符串格式，直接返回
                return alarm_time
            else:
                return str(alarm_time)
        except Exception:
            return str(alarm_time) if alarm_time else ""
    
    # 测试不同的时间格式
    test_times = [
        1735718400000,  # 毫秒时间戳
        1735718400,     # 秒时间戳
        "2025-01-01 12:00:00",  # 字符串格式
        None,           # 空值
        "",             # 空字符串
        0,              # 零值
    ]
    
    for time_value in test_times:
        formatted = format_alarm_time(time_value)
        print(f"输入: {time_value} -> 输出: '{formatted}'")

def test_csv_format():
    """测试CSV格式化函数"""
    print("\n=== 测试CSV格式化 ===")
    
    # 模拟format_csv_value函数
    def format_csv_value(value):
        try:
            if value is None or value == "":
                return ""
            
            # 如果是数字，确保不使用科学计数法
            if isinstance(value, (int, float)):
                if isinstance(value, float):
                    # 对于浮点数，如果是整数值就显示为整数
                    if value.is_integer():
                        return str(int(value))
                    else:
                        # 保留合理的小数位数
                        return f"{value:.6f}".rstrip('0').rstrip('.')
                else:
                    return str(value)
            
            # 如果是字符串形式的数字，检查是否需要特殊处理
            str_value = str(value)
            
            # 检查是否是纯数字字符串（可能很长的ID等）
            if str_value.isdigit() and len(str_value) > 10:
                # 对于长数字字符串，在前面加上单引号，防止Excel自动转换
                return f"'{str_value}"
            
            # 检查是否包含科学计数法
            if 'e' in str_value.lower() or 'E' in str_value:
                try:
                    # 尝试转换为数字再格式化
                    num_value = float(str_value)
                    if num_value.is_integer():
                        return str(int(num_value))
                    else:
                        return f"{num_value:.6f}".rstrip('0').rstrip('.')
                except ValueError:
                    pass
            
            return str_value
            
        except Exception:
            return str(value) if value is not None else ""
    
    # 测试不同的数值格式
    test_values = [
        123456789012345,  # 大整数
        1.23456789e+15,   # 科学计数法
        "1234567890123456789",  # 长数字字符串
        3.14159,          # 小数
        42.0,             # 整数值的浮点数
        "1.5e-5",         # 字符串形式的科学计数法
        None,             # 空值
        "",               # 空字符串
        "普通文本",        # 普通字符串
    ]
    
    for value in test_values:
        formatted = format_csv_value(value)
        print(f"输入: {value} ({type(value).__name__}) -> 输出: '{formatted}'")

def test_database_time_fields():
    """测试数据库中的时间字段"""
    print("\n=== 测试数据库时间字段 ===")
    
    try:
        conn = sqlite3.connect('zte_alarms.db')
        cursor = conn.cursor()
        
        # 获取一条记录检查时间字段
        cursor.execute("""
            SELECT alarm_raised_time, first_seen_at, last_seen_at, 
                   status_changed_at, created_at
            FROM alarms 
            WHERE is_active = 1 
            LIMIT 1
        """)
        
        result = cursor.fetchone()
        if result:
            fields = ['alarm_raised_time', 'first_seen_at', 'last_seen_at', 
                     'status_changed_at', 'created_at']
            
            print("数据库中的时间字段:")
            for i, field_name in enumerate(fields):
                value = result[i]
                print(f"  {field_name}: {value} ({type(value).__name__})")
                
                # 尝试格式化
                if value:
                    try:
                        if isinstance(value, (int, float)) and value > 1000000000:
                            if value > 1000000000000:
                                dt = datetime.fromtimestamp(value / 1000)
                            else:
                                dt = datetime.fromtimestamp(value)
                            formatted = dt.strftime("%Y-%m-%d %H:%M:%S")
                            print(f"    格式化后: {formatted}")
                    except Exception as e:
                        print(f"    格式化失败: {e}")
        else:
            print("没有找到活跃告警记录")
        
        conn.close()
        
    except Exception as e:
        print(f"数据库测试失败: {e}")

if __name__ == "__main__":
    print("=== 导出时间和数字格式修复测试 ===")
    
    test_time_format()
    test_csv_format()
    test_database_time_fields()
    
    print("\n=== 修复说明 ===")
    print("1. 添加了format_alarm_time方法处理时间戳转换")
    print("2. 添加了format_csv_value方法防止科学计数法")
    print("3. 修复了所有时间字段的导出格式")
    print("4. 长数字字符串会添加单引号防止Excel误解析")
    print("\n现在导出的时间应该正确显示，数字也不会变成科学计数法了！")
