# 导出时间和数字格式修复说明

## 问题描述
在PySide6告警监控程序的导出功能中存在以下问题：
1. **发生时间为空**：导出的CSV文件中时间列显示为空
2. **数字变成科学计数法**：大数字在Excel中显示为科学计数法（如1.23E+15）

## 问题分析

### 1. 时间字段为空的原因
- 数据库中存储的是时间戳（数字格式）
- 导出时直接使用 `str(alarm.get('alarm_raised_time', ''))` 
- 没有进行时间戳到可读时间的转换

### 2. 数字变成科学计数法的原因
- CSV文件中的大数字被Excel自动识别为数值
- Excel对于超过15位的数字自动使用科学计数法显示
- 长ID字符串也被误识别为数字

## 修复方案

### 1. 添加时间格式化方法
```python
def format_alarm_time(self, alarm_time):
    """格式化告警时间"""
    try:
        if not alarm_time:
            return ""
        
        if isinstance(alarm_time, (int, float)) and alarm_time > 0:
            # 处理时间戳
            if alarm_time > 1000000000000:  # 毫秒时间戳
                timestamp = alarm_time / 1000
            else:  # 秒时间戳
                timestamp = alarm_time
            
            dt = datetime.fromtimestamp(timestamp)
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        elif isinstance(alarm_time, str):
            # 如果已经是字符串格式，直接返回
            return alarm_time
        else:
            return str(alarm_time)
    except Exception:
        return str(alarm_time) if alarm_time else ""
```

### 2. 添加CSV值格式化方法
```python
def format_csv_value(self, value):
    """格式化CSV值，避免科学计数法和其他格式问题"""
    try:
        if value is None or value == "":
            return ""
        
        # 如果是数字，确保不使用科学计数法
        if isinstance(value, (int, float)):
            if isinstance(value, float):
                if value.is_integer():
                    return str(int(value))
                else:
                    return f"{value:.6f}".rstrip('0').rstrip('.')
            else:
                return str(value)
        
        str_value = str(value)
        
        # 检查是否是纯数字字符串（长ID等）
        if str_value.isdigit() and len(str_value) > 10:
            # 添加单引号防止Excel自动转换
            return f"'{str_value}"
        
        # 检查是否包含科学计数法
        if 'e' in str_value.lower() or 'E' in str_value:
            try:
                num_value = float(str_value)
                if num_value.is_integer():
                    return str(int(num_value))
                else:
                    return f"{num_value:.6f}".rstrip('0').rstrip('.')
            except ValueError:
                pass
        
        return str_value
        
    except Exception:
        return str(value) if value is not None else ""
```

### 3. 修复时间字段导出
更新所有时间相关字段的处理：
```python
elif col_index == 6:  # 发生时间
    return self.format_alarm_time(alarm.get('alarm_raised_time'))
elif col_index == 18:  # 首次发现
    return self.format_alarm_time(alarm.get('first_seen_at'))
elif col_index == 19:  # 最后发现
    return self.format_alarm_time(alarm.get('last_seen_at'))
elif col_index == 23:  # 状态变更时间
    return self.format_alarm_time(alarm.get('status_changed_at'))
elif col_index == 24:  # 创建时间
    return self.format_alarm_time(alarm.get('created_at'))
```

### 4. 应用格式化到导出流程
```python
# 写入数据
for alarm in self.alarms_data:
    row = []
    for col_index in selected_columns:
        value = self.get_alarm_column_value(alarm, col_index)
        # 处理数字格式，避免科学计数法
        formatted_value = self.format_csv_value(value)
        row.append(formatted_value)
    writer.writerow(row)
```

## 修复效果

### 时间字段修复
**修复前**：
- 发生时间：空白
- 首次发现：空白
- 状态变更时间：空白

**修复后**：
- 发生时间：2025-01-01 12:30:45
- 首次发现：2025-01-01 12:30:45
- 状态变更时间：2025-01-01 12:35:20

### 数字格式修复
**修复前**：
- 告警ID：1.23E+15
- 长数字：4.56E+18
- 小数：3.14159000

**修复后**：
- 告警ID：'1234567890123456789
- 长数字：'4567890123456789012
- 小数：3.14159

## 技术要点

### 时间戳处理
- 自动识别毫秒时间戳（>1000000000000）和秒时间戳
- 统一转换为 "YYYY-MM-DD HH:MM:SS" 格式
- 异常处理确保不会导致程序崩溃

### 数字格式处理
- 整数保持整数格式
- 浮点数去除尾随零
- 长数字字符串添加单引号前缀
- 科学计数法自动转换为普通数字

### Excel兼容性
- 单引号前缀防止Excel自动转换数字
- 保持原始数据精度
- 避免大数字显示问题

## 验证方法

1. **运行测试脚本**：
   ```bash
   python test_export_time_fix.py
   ```

2. **导出测试**：
   - 启动程序并导出数据
   - 检查时间列是否正确显示
   - 检查大数字是否保持原格式

3. **Excel验证**：
   - 在Excel中打开导出的CSV文件
   - 确认时间格式正确
   - 确认数字不是科学计数法

现在导出的时间应该正确显示，数字也不会变成科学计数法了！
