# 导出全部列数据修复说明

## 问题描述
在PySide6告警监控程序中，导出全部列时存在以下问题：
- 前面几十列有数据，但后面100多列只有表头没有数据
- 嵌套字段（如S-NSSAI.columnname）无法正确提取
- 复杂数据类型（字典、列表）处理不当

## 问题根源分析

### 1. 字段映射不完整
原始的 `get_raw_data_field_name` 方法只映射了27-54列：
```python
# 原始问题：只映射了部分字段
field_mapping = {
    27: "S-NSSAI",
    28: "id",
    # ... 只到54列
    54: "componentdn",
}
# 55-203列都返回"unknown"，导致无法获取数据
```

### 2. 缺少嵌套字段处理
TABLE_COLUMNS定义了很多嵌套字段，如：
- 113: "S-NSSAI列名" -> raw_data.S-NSSAI.columnname
- 150: "父级关联信息" -> raw_data.parentinfo.relation
- 203: "关联结果值" -> raw_data.relationresult.value

但原始代码无法处理这种嵌套结构。

### 3. 数据类型处理不当
raw_data中的某些字段是字典或列表，需要特殊处理。

## 修复方案

### 1. 完善字段映射
扩展 `get_raw_data_field_name` 方法，支持所有112个基础字段：

```python
field_mapping = {
    # 基础告警字段组（27-56）
    27: "S-NSSAI",
    28: "id",
    # ... 完整映射到112
    112: "aax_unrelationflag",
}
```

### 2. 添加嵌套字段处理
新增 `get_nested_field_value` 方法处理嵌套字段：

```python
def get_nested_field_value(self, raw_data, col_index):
    nested_field_mapping = {
        113: ("S-NSSAI", "columnname"),
        114: ("S-NSSAI", "datatype"),
        # ... 完整映射所有嵌套字段
        203: ("relationresult", "value"),
    }
    
    parent_field, sub_field = nested_field_mapping[col_index]
    parent_data = raw_data.get(parent_field)
    if isinstance(parent_data, dict):
        return str(parent_data.get(sub_field, ''))
```

### 3. 改进数据提取逻辑
新增 `get_raw_data_value` 方法统一处理数据提取：

```python
def get_raw_data_value(self, alarm, col_index):
    raw_data = alarm.get('raw_data')
    if not raw_data:
        return ""
    
    if col_index >= 113:  # 嵌套字段
        return self.get_nested_field_value(raw_data, col_index)
    else:  # 普通字段
        field_name = self.get_raw_data_field_name(col_index)
        value = raw_data.get(field_name, '')
        
        # 处理复杂数据类型
        if isinstance(value, (dict, list)):
            return json.dumps(value, ensure_ascii=False)
        else:
            return str(value) if value is not None else ""
```

## 修复后的功能特性

### 支持的列类型

1. **计算字段（0-7列）**
   - 状态、标记、考核持续时间等
   - 基于告警属性动态计算

2. **数据库字段（8-26列）**
   - 确认状态、告警类型、原因等
   - 直接从数据库字段获取

3. **基础告警字段（27-112列）**
   - 从raw_data的根级字段获取
   - 包括告警代码、网元信息等

4. **嵌套字段（113-203列）**
   - 从raw_data的嵌套对象中提取
   - 支持多层嵌套结构

### 数据类型处理

- **字符串/数字**：直接转换为字符串
- **字典**：转换为JSON字符串
- **列表**：转换为JSON字符串
- **空值**：返回空字符串

### 嵌套字段示例

```python
# 原始数据结构
raw_data = {
    "S-NSSAI": {
        "columnname": "slice_id",
        "datatype": "string",
        "value": "001-01"
    },
    "parentinfo": {
        "relation": "parent_alarm_123",
        "relation_2025_04": "rel_data"
    }
}

# 导出结果
# 列113: "slice_id"
# 列115: "S-NSSAI"  
# 列117: "001-01"
# 列150: "parent_alarm_123"
```

## 验证方法

1. **运行测试脚本**：
   ```bash
   python test_export_fix.py
   ```

2. **导出测试**：
   - 启动程序：`python alarm_monitor_pyside6.py`
   - 点击"📤 导出数据"
   - 选择"全选"
   - 检查导出的CSV文件是否包含所有203列的数据

3. **检查要点**：
   - CSV文件应有203列
   - 后面的列应该有实际数据，不只是空值
   - 嵌套字段应该正确提取

## 技术要点

### 字段映射策略
- 27-112列：直接字段映射
- 113-203列：嵌套字段映射
- 支持动态字段名生成

### 错误处理
- 所有数据提取都有异常处理
- 缺失字段返回空字符串
- 数据类型转换失败时返回原始值

### 性能优化
- 按需加载嵌套字段映射
- 避免重复的JSON解析
- 缓存字段映射结果

现在导出全部列时，所有203列都应该包含完整的数据了！
