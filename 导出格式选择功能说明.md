# 导出格式选择功能说明

## 功能概述
为PySide6告警监控程序添加了直观的导出格式选择对话框，让用户可以明确选择导出CSV还是XLSX格式。

## 新增功能

### 1. 格式选择对话框
用户点击"📤 导出数据"后，会弹出格式选择对话框：

#### 界面设计
- **标题**：清晰的"选择导出格式"标题
- **格式选项**：
  - 📊 **Excel格式 (.xlsx)**：默认选中
  - 📄 **CSV格式 (.csv)**：备选项
- **详细说明**：每种格式都有详细的特点介绍
- **依赖检查**：自动检测openpyxl库是否可用

#### 格式介绍
**XLSX格式优势**：
- ✅ 原生Excel格式，支持样式和格式化
- ✅ 表头美化，自动列宽调整
- ✅ 推荐用于数据分析和展示

**CSV格式优势**：
- ✅ 通用文本格式，兼容性最好
- ✅ 可在任何表格软件中打开
- ✅ 文件体积更小，处理速度快

### 2. 智能默认选择
- **有openpyxl**：默认选择XLSX格式
- **无openpyxl**：自动选择CSV格式，并显示安装提示

### 3. 依赖管理
- 自动检测openpyxl库的可用性
- 缺少依赖时显示友好的安装提示
- 提供具体的安装命令：`pip install openpyxl`

## 用户体验流程

### 完整导出流程
1. **点击导出按钮** → 📤 导出数据
2. **选择导出列** → 勾选需要的列
3. **选择文件格式** → 🆕 格式选择对话框
4. **选择保存位置** → 文件保存对话框
5. **完成导出** → 成功提示

### 格式选择步骤
1. **查看格式选项**：XLSX vs CSV
2. **阅读格式说明**：了解各自优势
3. **检查依赖提示**：确认XLSX是否可用
4. **做出选择**：点击对应的单选按钮
5. **确认导出**：点击"确定"按钮

## 技术实现

### 对话框创建
```python
def create_format_selection_dialog(self):
    """创建导出格式选择对话框"""
    dialog = QDialog(self)
    dialog.setWindowTitle("选择导出格式")
    dialog.selected_format = 'xlsx'  # 默认选择
    
    # XLSX选项
    xlsx_radio = QRadioButton("📊 Excel格式 (.xlsx)")
    xlsx_radio.setChecked(True)
    
    # CSV选项
    csv_radio = QRadioButton("📄 CSV格式 (.csv)")
    
    # 依赖检查
    try:
        import openpyxl
        xlsx_available = True
    except ImportError:
        xlsx_available = False
        # 显示安装提示
        # 默认选择CSV
    
    return dialog
```

### 导出流程控制
```python
# 选择导出格式
format_dialog = self.create_format_selection_dialog()
if format_dialog.exec() != QDialog.Accepted:
    return

export_format = format_dialog.selected_format

# 根据格式设置文件名和过滤器
if export_format == 'xlsx':
    default_filename = f"告警数据_{timestamp}.xlsx"
    file_filter = "Excel文件 (*.xlsx)"
else:
    default_filename = f"告警数据_{timestamp}.csv"
    file_filter = "CSV文件 (*.csv)"

# 调用对应的导出方法
if export_format == 'xlsx':
    self.export_to_xlsx(file_path, selected_columns)
else:
    self.export_to_csv(file_path, selected_columns)
```

## 界面样式

### 视觉设计
- **现代化UI**：圆角按钮，渐变色彩
- **图标支持**：📊 Excel图标，📄 CSV图标
- **颜色区分**：
  - 确定按钮：绿色 (#4CAF50)
  - 取消按钮：红色 (#f44336)
  - 警告提示：橙色 (#ff6b35)

### 响应式布局
- 对话框大小：400x300像素
- 自适应内容高度
- 按钮居右对齐
- 文字自动换行

## 错误处理

### 依赖缺失处理
```python
if not xlsx_available:
    warning_label = QLabel("⚠️ 注意：Excel格式需要安装openpyxl库\n安装命令：pip install openpyxl")
    warning_label.setStyleSheet("color: #ff6b35; background-color: #fff3e0; padding: 8px;")
    
    # 自动切换到CSV格式
    xlsx_radio.setChecked(False)
    csv_radio.setChecked(True)
    dialog.selected_format = 'csv'
```

### 用户取消处理
- 用户点击"取消"或关闭对话框时，终止导出流程
- 不会弹出文件保存对话框
- 记录用户取消操作

## 优势特点

### 用户友好
- ✅ **明确选择**：不再猜测导出格式
- ✅ **详细说明**：每种格式都有清晰介绍
- ✅ **智能提示**：自动检测依赖并给出建议
- ✅ **视觉直观**：图标和颜色帮助识别

### 技术优势
- ✅ **模块化设计**：格式选择与导出逻辑分离
- ✅ **错误处理**：完善的异常处理机制
- ✅ **向后兼容**：保持原有功能不变
- ✅ **扩展性好**：易于添加新的导出格式

### 维护性
- ✅ **代码清晰**：功能职责明确
- ✅ **样式统一**：使用一致的UI风格
- ✅ **易于测试**：独立的对话框组件
- ✅ **文档完善**：详细的使用说明

现在用户可以明确选择导出CSV还是XLSX格式，不再有格式选择的困惑！
