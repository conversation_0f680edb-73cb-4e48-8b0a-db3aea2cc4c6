#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证第一阶段修复效果
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def verify_fix():
    """验证修复效果"""
    try:
        # 导入主模块
        from alarm_monitor_pyside6 import TABLE_COLUMNS
        
        print("=" * 50)
        print("第一阶段修复验证")
        print("=" * 50)
        
        # 验证1：TABLE_COLUMNS定义
        print(f"TABLE_COLUMNS定义的列数: {len(TABLE_COLUMNS)}")
        
        # 验证2：检查关键列的定义
        key_columns = {
            0: "状态",
            1: "重点标记", 
            2: "关联标记",
            3: "告警名称",
            4: "根源ID",
            5: "级别",
            6: "网元名称",
            7: "IP地址",
            8: "发生时间",
            9: "考核持续时间",
            10: "运营商"
        }
        
        print("\n关键列验证:")
        all_match = True
        for idx, expected_name in key_columns.items():
            if idx < len(TABLE_COLUMNS):
                actual_name = TABLE_COLUMNS[idx]
                if actual_name == expected_name:
                    print(f"  列{idx:2d}: {expected_name} - OK")
                else:
                    print(f"  列{idx:2d}: 期望'{expected_name}' 实际'{actual_name}' - ERROR")
                    all_match = False
            else:
                print(f"  列{idx:2d}: 超出范围 - ERROR")
                all_match = False
        
        # 验证3：检查是否有重复的列名
        print(f"\n重复列名检查:")
        column_counts = {}
        for i, col_name in enumerate(TABLE_COLUMNS):
            if col_name in column_counts:
                column_counts[col_name].append(i)
            else:
                column_counts[col_name] = [i]
        
        duplicates = {name: indices for name, indices in column_counts.items() if len(indices) > 1}
        if duplicates:
            print("  发现重复列名:")
            for name, indices in duplicates.items():
                print(f"    '{name}': 列{indices}")
        else:
            print("  无重复列名 - OK")
        
        # 验证4：检查列数是否符合预期（203列）
        expected_count = 203
        actual_count = len(TABLE_COLUMNS)
        print(f"\n列数验证:")
        print(f"  期望列数: {expected_count}")
        print(f"  实际列数: {actual_count}")
        if actual_count == expected_count:
            print("  列数正确 - OK")
        else:
            print(f"  列数不匹配 - WARNING (差异: {actual_count - expected_count})")
        
        # 总结
        print("\n" + "=" * 50)
        if all_match and not duplicates:
            print("第一阶段修复验证通过")
            print("主要改进:")
            print("1. 统一了列填充逻辑")
            print("2. 修复了列索引映射问题") 
            print("3. 消除了重复的列填充代码")
            print("4. 添加了列映射验证机制")
        else:
            print("第一阶段修复存在问题，需要进一步调整")
        print("=" * 50)
        
        return all_match and not duplicates
        
    except Exception as e:
        print(f"验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = verify_fix()
    sys.exit(0 if success else 1)
