#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库性能优化效果
"""

import sqlite3
import time
import json
from datetime import datetime

def test_database_performance(db_file="zte_alarms.db"):
    """测试数据库查询性能"""
    print("🔍 开始测试数据库性能...")
    
    try:
        conn = sqlite3.connect(db_file, timeout=30.0)
        cursor = conn.cursor()
        
        # 检查数据库基本信息
        cursor.execute("SELECT COUNT(*) FROM alarms")
        total_count = cursor.fetchone()[0]
        print(f"📊 数据库总记录数: {total_count}")
        
        cursor.execute("SELECT COUNT(*) FROM alarms WHERE is_active = 1")
        active_count = cursor.fetchone()[0]
        print(f"📊 活跃告警数: {active_count}")
        
        # 检查索引是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='index' AND tbl_name='alarms'")
        indexes = [row[0] for row in cursor.fetchall()]
        print(f"📊 现有索引: {indexes}")
        
        # 测试各种查询的性能
        queries = [
            ("基础计数查询", "SELECT COUNT(*) FROM alarms WHERE is_active = 1"),
            ("新告警统计", "SELECT COUNT(*) FROM alarms WHERE is_active = 1 AND is_new = 1"),
            ("基线告警统计", "SELECT COUNT(*) FROM alarms WHERE is_active = 1 AND is_baseline = 1"),
            ("JSON查询(根源)", "SELECT COUNT(*) FROM alarms WHERE is_active = 1 AND json_extract(raw_data, '$.relationflag') = 1"),
            ("JSON查询(衍生)", "SELECT COUNT(*) FROM alarms WHERE is_active = 1 AND json_extract(raw_data, '$.relationflag') = 2"),
        ]
        
        print("\n⏱️ 查询性能测试:")
        for name, query in queries:
            start_time = time.time()
            try:
                cursor.execute(query)
                result = cursor.fetchone()[0]
                end_time = time.time()
                duration = end_time - start_time
                print(f"  {name}: {result} 条 (耗时: {duration:.3f}秒)")
                
                if duration > 5.0:
                    print(f"    ⚠️ 查询较慢，可能需要优化")
                elif duration > 1.0:
                    print(f"    ⚡ 查询稍慢")
                else:
                    print(f"    ✅ 查询快速")
                    
            except Exception as e:
                print(f"    ❌ 查询失败: {e}")
        
        # 检查是否有预处理字段
        cursor.execute("PRAGMA table_info(alarms)")
        columns = [row[1] for row in cursor.fetchall()]
        
        has_raw_relationflag = 'raw_relationflag' in columns
        print(f"\n📊 预处理字段状态:")
        print(f"  raw_relationflag 字段: {'✅ 存在' if has_raw_relationflag else '❌ 不存在'}")
        
        if has_raw_relationflag:
            # 测试预处理字段查询
            start_time = time.time()
            cursor.execute("SELECT COUNT(*) FROM alarms WHERE is_active = 1 AND raw_relationflag = '1'")
            result = cursor.fetchone()[0]
            end_time = time.time()
            duration = end_time - start_time
            print(f"  预处理字段查询(根源): {result} 条 (耗时: {duration:.3f}秒)")
        
        conn.close()
        print("\n✅ 性能测试完成")
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")

def create_test_indexes(db_file="zte_alarms.db"):
    """创建测试索引"""
    print("🔧 创建性能优化索引...")
    
    try:
        conn = sqlite3.connect(db_file, timeout=30.0)
        cursor = conn.cursor()
        
        # 创建索引
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_is_active ON alarms(is_active)",
            "CREATE INDEX IF NOT EXISTS idx_is_active_baseline ON alarms(is_active, is_baseline)",
            "CREATE INDEX IF NOT EXISTS idx_code_name ON alarms(code_name)",
        ]
        
        for index_sql in indexes:
            try:
                start_time = time.time()
                cursor.execute(index_sql)
                end_time = time.time()
                print(f"  ✅ 索引创建成功 (耗时: {end_time - start_time:.3f}秒)")
            except Exception as e:
                print(f"  ⚠️ 索引创建跳过: {e}")
        
        # 更新统计信息
        print("📊 更新查询优化器统计信息...")
        cursor.execute("ANALYZE")
        
        conn.commit()
        conn.close()
        print("✅ 索引创建完成")
        
    except Exception as e:
        print(f"❌ 索引创建失败: {e}")

if __name__ == "__main__":
    print("🚀 数据库性能优化测试工具")
    print("=" * 50)
    
    # 测试当前性能
    print("\n1️⃣ 测试当前性能:")
    test_database_performance()
    
    # 创建优化索引
    print("\n2️⃣ 创建优化索引:")
    create_test_indexes()
    
    # 再次测试性能
    print("\n3️⃣ 测试优化后性能:")
    test_database_performance()
    
    print("\n🎉 测试完成！")
