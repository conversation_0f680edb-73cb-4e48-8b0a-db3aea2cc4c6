# PySide6导出功能修复说明

## 问题描述
在PySide6告警监控程序中，导出表格功能存在以下问题：
- 即使用户选择了"全部列"，导出的CSV文件也只包含固定的8列
- 无法根据用户的列选择来动态导出数据
- 导出的列数与界面显示的列数不匹配

## 问题原因
原始的 `export_data` 方法中：
1. **硬编码表头**：表头固定为8列，不考虑用户选择
2. **硬编码数据行**：数据行也固定提取8个字段
3. **缺少列选择界面**：用户无法选择要导出的列

```python
# 原始问题代码
headers = ["状态", "标记", "告警名称", "级别", "网元", "IP地址", "发生时间", "考核持续时间"]  # 固定8列
```

## 修复方案

### 1. 添加列选择对话框
- 创建一个对话框让用户选择要导出的列
- 提供"全选"、"全不选"、"选择可见列"等快捷按钮
- 默认选中当前界面可见的列

### 2. 动态生成表头和数据
- 根据用户选择的列动态生成CSV表头
- 根据列索引动态提取对应的数据值

### 3. 完善数据映射
- 添加 `get_alarm_column_value` 方法，根据列索引获取对应数据
- 支持所有203列的数据导出
- 正确处理计算字段（状态、标记等）和原始数据字段

## 修复后的功能特性

### 列选择对话框
```python
# 创建列选择对话框
dialog = QDialog(self)
dialog.setWindowTitle("选择导出列")

# 为每列创建复选框
for i, column_name in enumerate(TABLE_COLUMNS):
    checkbox = QCheckBox(f"{column_name}")
    checkbox.setChecked(not self.table.isColumnHidden(i))  # 默认选中可见列
```

### 动态表头生成
```python
# 根据选中的列生成表头
headers = [TABLE_COLUMNS[i] for i in selected_columns]
writer.writerow(headers)
```

### 智能数据提取
```python
def get_alarm_column_value(self, alarm, col_index):
    """根据列索引获取告警数据的对应值"""
    if col_index == 0:  # 状态
        if alarm.get('is_baseline', 0):
            return "📊 基线"
        elif alarm.get('is_new', 0):
            return "🆕 新的"
        # ... 更多状态判断
    elif col_index == 1:  # 标记
        # 动态生成标记（根源、衍生、重点等）
    # ... 支持所有203列
```

## 使用方法

### 1. 启动导出
点击"📤 导出数据"按钮

### 2. 选择列
- **全选**：导出所有203列
- **全不选**：清除所有选择
- **选择可见列**：只导出当前界面显示的列
- **手动选择**：勾选需要的列

### 3. 选择文件
选择CSV文件保存位置

### 4. 完成导出
系统会显示导出的记录数和列数

## 支持的列类型

### 计算字段（0-7列）
- 状态：基线/新的/持续/已清除
- 标记：根源/衍生/重点关注
- 考核持续时间：格式化显示

### 数据库字段（8-26列）
- 直接从数据库字段获取
- 包括确认状态、告警类型、原因等

### 原始数据字段（27-203列）
- 从raw_data JSON中提取
- 包括所有网管系统的原始字段

## 优势

1. **完全可定制**：用户可以选择任意列组合进行导出
2. **数据完整性**：支持导出所有203列的完整数据
3. **用户友好**：提供直观的列选择界面
4. **性能优化**：只导出用户需要的列，减少文件大小
5. **格式一致**：导出的数据格式与界面显示保持一致

## 文件信息
- 修复文件：`alarm_monitor_pyside6.py`
- 新增方法：
  - `export_data()` - 重写的导出方法
  - `get_alarm_column_value()` - 数据值获取方法
  - `get_raw_data_field_name()` - 原始数据字段映射
  - `format_duration_text()` - 持续时间格式化

现在导出功能可以完美支持用户选择的任意列组合！
