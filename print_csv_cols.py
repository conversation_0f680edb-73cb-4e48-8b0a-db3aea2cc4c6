import csv
want = {'alarm_key': ['告警键','告警ID','alarm_key'],
        'relationflag': ['关联标志','relationflag'],
        'relationflagname': ['关联标记','关联标志名','relationflagname'],
        'parent_relation': ['父级关联信息','父级关联','parentinfo.relation']}
with open('告警数据_20250812_055013.csv','r',encoding='utf-8-sig') as f:
    r=csv.reader(f); hdr=next(r); idx={}
    for k, keys in want.items():
        idx[k]=next((i for i,h in enumerate(hdr) if any(s in h for s in keys)), None)
    print('匹配到的索引:', idx); 
    for n,row in zip(range(5), r): print([row[i] if i is not None else '' for i in idx.values()])