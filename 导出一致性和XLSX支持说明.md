# 导出一致性和XLSX支持修复说明

## 问题描述
1. **数据不一致**：导出的发生时间和考核持续时间与表格显示不一致
2. **格式限制**：只支持CSV导出，不支持XLSX格式

## 问题根源分析

### 数据不一致的原因
**表格显示逻辑**：
```python
# 第6列 - 发生时间
QTableWidgetItem(str(alarm.get('time_str', '')))

# 第7列 - 考核持续时间  
QTableWidgetItem(str(alarm.get('duration_str', '')))
```

**原始导出逻辑**：
```python
# 第6列 - 发生时间
return self.format_alarm_time(alarm.get('alarm_raised_time'))

# 第7列 - 考核持续时间
return self.format_duration_text(alarm.get('effective_duration_minutes'))
```

**问题**：表格使用已格式化的字符串，导出使用原始数据重新格式化，导致结果不一致。

## 修复方案

### 1. 统一数据源
修改导出逻辑，优先使用与表格显示相同的数据源：

```python
elif col_index == 6:  # 发生时间
    # 优先使用已格式化的时间字符串，与表格显示保持一致
    time_str = alarm.get('time_str', '')
    if time_str:
        return str(time_str)
    else:
        # 如果没有格式化字符串，则从原始时间戳转换
        return self.format_alarm_time(alarm.get('alarm_raised_time'))

elif col_index == 7:  # 考核持续时间
    # 优先使用已格式化的持续时间字符串，与表格显示保持一致
    duration_str = alarm.get('duration_str', '')
    if duration_str:
        return str(duration_str)
    else:
        # 如果没有格式化字符串，则从分钟数转换
        effective_duration = alarm.get('effective_duration_minutes')
        if effective_duration is not None:
            return self.format_duration_text(effective_duration)
        else:
            return ""
```

### 2. 添加XLSX导出支持

#### 文件格式选择
```python
file_path, selected_filter = QFileDialog.getSaveFileName(
    self,
    "导出告警数据",
    f"告警数据_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
    "Excel文件 (*.xlsx);;CSV文件 (*.csv)"
)
```

#### XLSX导出功能
```python
def export_to_xlsx(self, file_path, selected_columns):
    """导出到Excel文件"""
    try:
        import openpyxl
        from openpyxl.styles import Font, Alignment, PatternFill
        from openpyxl.utils import get_column_letter
    except ImportError:
        # 如果没有openpyxl，自动降级到CSV
        QMessageBox.warning(self, "缺少依赖", 
                          "导出Excel文件需要安装openpyxl库:\n"
                          "pip install openpyxl")
        csv_path = file_path.replace('.xlsx', '.csv')
        self.export_to_csv(csv_path, selected_columns)
        return

    # 创建工作簿和工作表
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "告警数据"

    # 设置表头样式
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = Font(bold=True, color="FFFFFF")
        cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        cell.alignment = Alignment(horizontal="center", vertical="center")

    # 写入数据并设置格式
    for row_idx, alarm in enumerate(self.alarms_data, 2):
        for col_idx, col_index in enumerate(selected_columns, 1):
            value = self.get_alarm_column_value(alarm, col_index)
            cell = ws.cell(row=row_idx, column=col_idx, value=value)
            
            # 根据列类型设置对齐方式
            if col_index in [6, 18, 19, 23, 24]:  # 时间列
                cell.alignment = Alignment(horizontal="left")
            elif col_index in [0, 1]:  # 状态和标记列
                cell.alignment = Alignment(horizontal="center")

    # 自动调整列宽
    for col in range(1, len(headers) + 1):
        # 计算合适的列宽
        adjusted_width = min(max_length + 2, 50)
        ws.column_dimensions[column_letter].width = adjusted_width

    wb.save(file_path)
```

## 新功能特性

### 导出格式支持
1. **CSV格式**：
   - UTF-8编码，支持中文
   - 兼容Excel和其他表格软件
   - 处理科学计数法问题

2. **XLSX格式**：
   - 原生Excel格式
   - 支持样式和格式化
   - 自动列宽调整
   - 表头样式美化

### 数据一致性保证
- ✅ 发生时间与表格显示完全一致
- ✅ 考核持续时间与表格显示完全一致
- ✅ 所有字段都使用相同的数据源
- ✅ 格式化逻辑统一

### XLSX特色功能
- 🎨 **表头样式**：蓝色背景，白色粗体文字
- 📏 **自动列宽**：根据内容自动调整，最大50字符
- 🔤 **数据对齐**：时间左对齐，状态居中对齐
- 📊 **数据类型**：正确处理数字、文本、日期

### 依赖管理
- 自动检测openpyxl库
- 缺少依赖时自动降级到CSV
- 友好的错误提示和安装指导

## 使用方法

### 1. 安装依赖（可选）
```bash
pip install openpyxl
```

### 2. 导出数据
1. 点击"📤 导出数据"按钮
2. 选择要导出的列
3. 选择文件格式（XLSX或CSV）
4. 选择保存位置

### 3. 文件格式选择
- **推荐XLSX**：更好的格式化和样式
- **使用CSV**：更好的兼容性和更小的文件

## 技术优势

### 数据一致性
- 表格显示和导出使用相同数据源
- 避免重复格式化导致的差异
- 保证用户看到的就是导出的

### 格式兼容性
- XLSX：原生Excel支持，样式丰富
- CSV：通用格式，兼容性最好
- 自动降级机制，确保总能导出

### 用户体验
- 直观的格式选择
- 自动文件扩展名
- 清晰的错误提示
- 进度反馈

现在导出的数据与表格显示完全一致，并且支持美观的XLSX格式！
