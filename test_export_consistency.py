#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试导出数据一致性的脚本
"""

import sqlite3
import json

def test_data_consistency():
    """测试表格显示和导出数据的一致性"""
    print("=== 测试数据一致性 ===")
    
    try:
        conn = sqlite3.connect('zte_alarms.db')
        cursor = conn.cursor()
        
        # 获取一条活跃告警记录
        cursor.execute("""
            SELECT id, code_name, alarm_raised_time, effective_duration_minutes,
                   time_str, duration_str, ne_ip, me_name
            FROM alarms 
            WHERE is_active = 1 
            LIMIT 1
        """)
        
        result = cursor.fetchone()
        if result:
            alarm_id, code_name, alarm_raised_time, effective_duration_minutes, time_str, duration_str, ne_ip, me_name = result
            
            print(f"告警ID: {alarm_id}")
            print(f"告警名称: {code_name}")
            print()
            
            print("=== 时间字段对比 ===")
            print(f"原始时间戳: {alarm_raised_time} ({type(alarm_raised_time).__name__})")
            print(f"格式化时间: {time_str}")
            print()
            
            print("=== 持续时间字段对比 ===")
            print(f"原始分钟数: {effective_duration_minutes}")
            print(f"格式化持续时间: {duration_str}")
            print()
            
            print("=== 其他字段对比 ===")
            print(f"网元名称: {me_name}")
            print(f"IP地址: {ne_ip}")
            
            # 模拟导出逻辑
            print("\n=== 导出逻辑测试 ===")
            
            # 模拟alarm数据结构
            alarm = {
                'time_str': time_str,
                'duration_str': duration_str,
                'alarm_raised_time': alarm_raised_time,
                'effective_duration_minutes': effective_duration_minutes,
                'ne_ip': ne_ip,
                'me_name': me_name,
                'code_name': code_name
            }
            
            # 测试第6列（发生时间）
            col6_value = get_column_value(alarm, 6)
            print(f"第6列（发生时间）导出值: {col6_value}")
            
            # 测试第7列（考核持续时间）
            col7_value = get_column_value(alarm, 7)
            print(f"第7列（考核持续时间）导出值: {col7_value}")
            
            # 检查一致性
            print("\n=== 一致性检查 ===")
            time_consistent = (col6_value == time_str) if time_str else True
            duration_consistent = (col7_value == duration_str) if duration_str else True
            
            print(f"时间字段一致性: {'✅ 一致' if time_consistent else '❌ 不一致'}")
            print(f"持续时间字段一致性: {'✅ 一致' if duration_consistent else '❌ 不一致'}")
            
        else:
            print("没有找到活跃告警记录")
        
        conn.close()
        
    except Exception as e:
        print(f"测试失败: {e}")

def get_column_value(alarm, col_index):
    """模拟get_alarm_column_value方法的逻辑"""
    if col_index == 6:  # 发生时间
        # 优先使用已格式化的时间字符串
        time_str = alarm.get('time_str', '')
        if time_str:
            return str(time_str)
        else:
            # 如果没有格式化字符串，则从原始时间戳转换
            return format_alarm_time(alarm.get('alarm_raised_time'))
    elif col_index == 7:  # 考核持续时间
        # 优先使用已格式化的持续时间字符串
        duration_str = alarm.get('duration_str', '')
        if duration_str:
            return str(duration_str)
        else:
            # 如果没有格式化字符串，则从分钟数转换
            effective_duration = alarm.get('effective_duration_minutes')
            if effective_duration is not None:
                return format_duration_text(effective_duration)
            else:
                return ""
    else:
        return ""

def format_alarm_time(alarm_time):
    """模拟时间格式化"""
    from datetime import datetime
    try:
        if not alarm_time:
            return ""
        
        if isinstance(alarm_time, (int, float)) and alarm_time > 0:
            if alarm_time > 1000000000000:  # 毫秒时间戳
                timestamp = alarm_time / 1000
            else:  # 秒时间戳
                timestamp = alarm_time
            
            dt = datetime.fromtimestamp(timestamp)
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        elif isinstance(alarm_time, str):
            return alarm_time
        else:
            return str(alarm_time)
    except Exception:
        return str(alarm_time) if alarm_time else ""

def format_duration_text(minutes):
    """模拟持续时间格式化"""
    if minutes is None:
        return ""
    
    try:
        minutes = int(minutes)
        if minutes < 60:
            return f"{minutes}分钟"
        elif minutes < 1440:  # 小于24小时
            hours = minutes // 60
            mins = minutes % 60
            return f"{hours}小时{mins}分钟" if mins > 0 else f"{hours}小时"
        else:  # 大于等于24小时
            days = minutes // 1440
            hours = (minutes % 1440) // 60
            mins = minutes % 60
            result = f"{days}天"
            if hours > 0:
                result += f"{hours}小时"
            if mins > 0:
                result += f"{mins}分钟"
            return result
    except (ValueError, TypeError):
        return str(minutes)

def test_xlsx_support():
    """测试XLSX支持"""
    print("\n=== 测试XLSX支持 ===")
    
    try:
        import openpyxl
        print("✅ openpyxl库已安装，支持XLSX导出")
        print(f"openpyxl版本: {openpyxl.__version__}")
    except ImportError:
        print("❌ openpyxl库未安装")
        print("安装命令: pip install openpyxl")

if __name__ == "__main__":
    print("=== 导出一致性和XLSX支持测试 ===")
    
    test_data_consistency()
    test_xlsx_support()
    
    print("\n=== 修复说明 ===")
    print("1. 修复了发生时间和考核持续时间的导出不一致问题")
    print("2. 导出时优先使用已格式化的字符串（time_str, duration_str）")
    print("3. 添加了XLSX导出支持，包括样式和格式化")
    print("4. 提供CSV和XLSX两种导出格式选择")
    print("\n现在导出的数据应该与表格显示完全一致了！")
