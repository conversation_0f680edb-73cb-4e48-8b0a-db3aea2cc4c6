#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试列映射修复效果
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_column_mapping():
    """测试列映射一致性"""
    try:
        # 导入主模块
        from alarm_monitor_pyside6 import TABLE_COLUMNS, AlarmMonitorGUI
        
        print("=" * 60)
        print("列映射修复测试")
        print("=" * 60)
        
        # 测试1：检查TABLE_COLUMNS定义
        print(f"TABLE_COLUMNS定义的列数: {len(TABLE_COLUMNS)}")
        print(f"前10列: {TABLE_COLUMNS[:10]}")
        print(f"后10列: {TABLE_COLUMNS[-10:]}")

        # 测试2：创建模拟告警数据
        mock_alarm = {
            'status_marks': '新的',
            'focus_marks': '重点',
            'relation_marks': '根源',
            'code_name': '测试告警',
            'root_group_id': 'test-root-id-12345678901234567890',
            'severity': '严重',
            'me_name': '测试网元',
            'ne_ip': '***********',
            'time_str': '01-15 10:30',
            'duration_str': '2小时30分钟',
            'operator_info': '联通',
            'alarm_key': 'test-alarm-key',
            'is_new': 1,
            'is_active': 1,
            'raw_s_nssai': 'test-nssai',
            'raw_id': 'test-id',
        }
        
        # 测试3：创建GUI实例（不显示界面）
        from PySide6.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        gui = AlarmMonitorGUI()
        
        # 测试4：验证get_unified_column_value方法
        print("\n测试get_unified_column_value方法:")
        test_columns = [0, 1, 2, 3, 4, 5, 10, 30, 50, 100, 150, 200, 202]

        for col_idx in test_columns:
            if col_idx < len(TABLE_COLUMNS):
                try:
                    value = gui.get_unified_column_value(mock_alarm, col_idx)
                    col_name = TABLE_COLUMNS[col_idx]
                    print(f"  列{col_idx:3d} ({col_name[:20]:20s}): {str(value)[:30]}")
                except Exception as e:
                    print(f"  列{col_idx:3d} 错误: {e}")
            else:
                print(f"  列{col_idx:3d} 超出范围")

        # 测试5：验证表格创建
        print(f"\n表格列数验证:")
        expected_columns = len(TABLE_COLUMNS)
        actual_columns = gui.table.columnCount()
        print(f"  期望列数: {expected_columns}")
        print(f"  实际列数: {actual_columns}")

        if expected_columns == actual_columns:
            print("  列数匹配 - OK")
        else:
            print("  列数不匹配 - ERROR")

        # 测试6：验证列标题
        print(f"\n列标题验证:")
        header_match = True
        for i in range(min(expected_columns, actual_columns)):
            expected_header = TABLE_COLUMNS[i]
            actual_header = gui.table.horizontalHeaderItem(i).text() if gui.table.horizontalHeaderItem(i) else ""
            if expected_header != actual_header:
                print(f"  列{i}: 期望'{expected_header}' 实际'{actual_header}'")
                header_match = False

        if header_match:
            print("  所有列标题匹配 - OK")
        else:
            print("  存在列标题不匹配 - ERROR")

        print("\n" + "=" * 60)
        print("列映射修复测试完成")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_column_mapping()
    sys.exit(0 if success else 1)
